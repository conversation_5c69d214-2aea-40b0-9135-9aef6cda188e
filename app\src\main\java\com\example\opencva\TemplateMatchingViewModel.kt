package com.example.opencva

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.net.Uri
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

/**
 * 模板匹配应用的ViewModel
 */
class TemplateMatchingViewModel : ViewModel() {
    
    private val templateMatcher = TemplateMatcher()
    
    private val _uiState = MutableStateFlow(TemplateMatchingUiState())
    val uiState: StateFlow<TemplateMatchingUiState> = _uiState.asStateFlow()
    
    /**
     * 初始化匹配器
     */
    fun initializeMatcher() {
        viewModelScope.launch {
            val result = templateMatcher.initialize()
            result.onSuccess {
                _uiState.value = _uiState.value.copy(
                    isInitialized = true,
                    gpuAvailable = templateMatcher.isGpuAvailable()
                )
            }.onFailure { error ->
                _uiState.value = _uiState.value.copy(
                    errorMessage = "初始化失败: ${error.message}"
                )
            }
        }
    }
    
    /**
     * 设置源图像
     */
    fun setSourceImage(context: Context, uri: Uri) {
        viewModelScope.launch {
            try {
                val inputStream = context.contentResolver.openInputStream(uri)
                val bitmap = BitmapFactory.decodeStream(inputStream)
                inputStream?.close()
                
                if (bitmap != null) {
                    _uiState.value = _uiState.value.copy(
                        sourceImage = bitmap,
                        errorMessage = null
                    )
                } else {
                    _uiState.value = _uiState.value.copy(
                        errorMessage = "无法加载源图像"
                    )
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    errorMessage = "加载源图像失败: ${e.message}"
                )
            }
        }
    }
    
    /**
     * 设置模板图像
     */
    fun setTemplateImage(context: Context, uri: Uri) {
        viewModelScope.launch {
            try {
                val inputStream = context.contentResolver.openInputStream(uri)
                val bitmap = BitmapFactory.decodeStream(inputStream)
                inputStream?.close()
                
                if (bitmap != null) {
                    _uiState.value = _uiState.value.copy(
                        templateImage = bitmap,
                        errorMessage = null
                    )
                } else {
                    _uiState.value = _uiState.value.copy(
                        errorMessage = "无法加载模板图像"
                    )
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    errorMessage = "加载模板图像失败: ${e.message}"
                )
            }
        }
    }
    
    /**
     * 更新匹配配置
     */
    fun updateConfig(config: TemplateMatcher.MatchConfig) {
        _uiState.value = _uiState.value.copy(matchConfig = config)
    }
    
    /**
     * 设置预设模式
     */
    fun setPresetMode(mode: TemplateMatcher.PresetMode) {
        val config = TemplateMatcher.MatchConfig.fromPreset(mode)
        _uiState.value = _uiState.value.copy(
            presetMode = mode,
            matchConfig = config
        )
    }
    
    /**
     * 执行模板匹配
     */
    suspend fun performMatching() {
        val currentState = _uiState.value
        val sourceImage = currentState.sourceImage
        val templateImage = currentState.templateImage
        
        if (sourceImage == null || templateImage == null) {
            _uiState.value = currentState.copy(
                errorMessage = "请先选择源图像和模板图像"
            )
            return
        }
        
        if (!currentState.isInitialized) {
            _uiState.value = currentState.copy(
                errorMessage = "匹配器未初始化"
            )
            return
        }
        
        _uiState.value = currentState.copy(
            isMatching = true,
            errorMessage = null
        )
        
        val result = templateMatcher.matchTemplate(
            sourceImage,
            templateImage,
            currentState.matchConfig
        )
        
        result.onSuccess { matchResult ->
            _uiState.value = _uiState.value.copy(
                isMatching = false,
                matchResult = matchResult,
                performanceStats = matchResult.performanceStats
            )
        }.onFailure { error ->
            _uiState.value = _uiState.value.copy(
                isMatching = false,
                errorMessage = "匹配失败: ${error.message}"
            )
        }
    }
    
    /**
     * 运行基准测试
     */
    suspend fun runBenchmark() {
        val currentState = _uiState.value
        val sourceImage = currentState.sourceImage
        val templateImage = currentState.templateImage
        
        if (sourceImage == null || templateImage == null) {
            _uiState.value = currentState.copy(
                errorMessage = "请先选择源图像和模板图像"
            )
            return
        }
        
        _uiState.value = currentState.copy(
            isMatching = true,
            errorMessage = null
        )
        
        val result = templateMatcher.benchmark(
            sourceImage,
            templateImage,
            iterations = 10,
            currentState.matchConfig
        )
        
        result.onSuccess { benchmarkResult ->
            _uiState.value = _uiState.value.copy(
                isMatching = false,
                benchmarkResult = benchmarkResult
            )
        }.onFailure { error ->
            _uiState.value = _uiState.value.copy(
                isMatching = false,
                errorMessage = "基准测试失败: ${error.message}"
            )
        }
    }
    
    override fun onCleared() {
        super.onCleared()
        templateMatcher.destroy()
    }
}

/**
 * UI状态数据类
 */
data class TemplateMatchingUiState(
    val isInitialized: Boolean = false,
    val gpuAvailable: Boolean = false,
    val sourceImage: Bitmap? = null,
    val templateImage: Bitmap? = null,
    val matchConfig: TemplateMatcher.MatchConfig = TemplateMatcher.MatchConfig.default(),
    val presetMode: TemplateMatcher.PresetMode = TemplateMatcher.PresetMode.BALANCED,
    val isMatching: Boolean = false,
    val matchResult: TemplateMatchResult? = null,
    val performanceStats: PerformanceStats? = null,
    val benchmarkResult: BenchmarkResult? = null,
    val errorMessage: String? = null
)
