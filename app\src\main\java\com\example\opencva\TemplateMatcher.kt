package com.example.opencva

import android.graphics.Bitmap
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import java.util.concurrent.atomic.AtomicBoolean

/**
 * 高级模板匹配器API
 * 提供用户友好的接口，支持异步操作和协程
 */
class TemplateMatcher {
    
    private val nativeMatcher = TemplateMatcherNative()
    private val isInitialized = AtomicBoolean(false)
    private val matchingScope = CoroutineScope(Dispatchers.Default + SupervisorJob())
    
    /**
     * 预设参数配置
     */
    enum class PresetMode(
        val displayName: String,
        val weakThreshold: Float,
        val strongThreshold: Float,
        val maxResults: Int,
        val method: Int
    ) {
        FAST("快速模式", 0.6f, 0.8f, 5, TemplateMatcherNative.MatchMethod.CCORR_NORMED),
        BALANCED("平衡模式", 0.5f, 0.75f, 10, TemplateMatcherNative.MatchMethod.CCOEFF_NORMED),
        ACCURATE("精确模式", 0.3f, 0.7f, 15, TemplateMatcherNative.MatchMethod.CCOEFF_NORMED),
        SENSITIVE("敏感模式", 0.2f, 0.5f, 20, TemplateMatcherNative.MatchMethod.CCOEFF_NORMED)
    }
    
    /**
     * 匹配参数配置类
     */
    data class MatchConfig(
        val method: Int = TemplateMatcherNative.MatchMethod.CCOEFF_NORMED,
        val weakThreshold: Float = 0.5f,
        val strongThreshold: Float = 0.8f,
        val scaleFactor: Float = 1.0f,
        val maxResults: Int = 10,
        val searchRegion: SearchRegion? = null,
        val enableGpu: Boolean = true
    ) {
        companion object {
            /**
             * 从预设模式创建配置
             */
            fun fromPreset(preset: PresetMode): MatchConfig {
                return MatchConfig(
                    method = preset.method,
                    weakThreshold = preset.weakThreshold,
                    strongThreshold = preset.strongThreshold,
                    maxResults = preset.maxResults
                )
            }
            
            /**
             * 默认配置
             */
            fun default(): MatchConfig = fromPreset(PresetMode.BALANCED)
        }
        
        /**
         * 验证配置参数
         */
        fun validate(): Result<Unit> {
            return when {
                weakThreshold < 0f || weakThreshold > 1f -> 
                    Result.failure(IllegalArgumentException("弱阈值必须在0.0-1.0之间"))
                strongThreshold < 0f || strongThreshold > 1f -> 
                    Result.failure(IllegalArgumentException("强阈值必须在0.0-1.0之间"))
                weakThreshold > strongThreshold -> 
                    Result.failure(IllegalArgumentException("弱阈值不能大于强阈值"))
                scaleFactor <= 0f || scaleFactor > 2f -> 
                    Result.failure(IllegalArgumentException("缩放因子必须在0.0-2.0之间"))
                maxResults <= 0 || maxResults > 100 -> 
                    Result.failure(IllegalArgumentException("最大结果数必须在1-100之间"))
                else -> Result.success(Unit)
            }
        }
    }
    
    /**
     * 初始化匹配器
     */
    suspend fun initialize(): Result<Unit> = withContext(Dispatchers.Default) {
        try {
            if (isInitialized.get()) {
                return@withContext Result.success(Unit)
            }
            
            val success = nativeMatcher.initialize()
            if (success) {
                isInitialized.set(true)
                Result.success(Unit)
            } else {
                Result.failure(RuntimeException("Failed to initialize native matcher"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 销毁匹配器
     */
    fun destroy() {
        if (isInitialized.compareAndSet(true, false)) {
            nativeMatcher.destroy()
            matchingScope.cancel()
        }
    }
    
    /**
     * 检查是否已初始化
     */
    fun isInitialized(): Boolean = isInitialized.get()
    
    /**
     * 检查GPU是否可用
     */
    fun isGpuAvailable(): Boolean {
        return if (isInitialized.get()) {
            nativeMatcher.isGpuAvailable()
        } else false
    }
    
    /**
     * 同步执行模板匹配
     */
    suspend fun matchTemplate(
        sourceBitmap: Bitmap,
        templateBitmap: Bitmap,
        config: MatchConfig = MatchConfig.default()
    ): Result<TemplateMatchResult> = withContext(Dispatchers.Default) {
        try {
            if (!isInitialized.get()) {
                return@withContext Result.failure(IllegalStateException("Matcher not initialized"))
            }
            
            // 验证配置
            config.validate().getOrElse { 
                return@withContext Result.failure(it)
            }
            
            // 验证输入
            if (sourceBitmap.isRecycled || templateBitmap.isRecycled) {
                return@withContext Result.failure(IllegalArgumentException("Bitmap已被回收"))
            }
            
            if (templateBitmap.width > sourceBitmap.width || templateBitmap.height > sourceBitmap.height) {
                return@withContext Result.failure(IllegalArgumentException("模板图像不能大于源图像"))
            }
            
            // 设置GPU状态
            nativeMatcher.setGpuEnabled(config.enableGpu)
            
            // 执行匹配
            val result = nativeMatcher.matchTemplate(
                sourceBitmap = sourceBitmap,
                templateBitmap = templateBitmap,
                method = config.method,
                weakThreshold = config.weakThreshold,
                strongThreshold = config.strongThreshold,
                scaleFactor = config.scaleFactor,
                maxResults = config.maxResults,
                searchRegion = config.searchRegion
            )
            
            if (result != null) {
                Result.success(result)
            } else {
                Result.failure(RuntimeException("Native matching failed"))
            }
            
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 异步执行模板匹配
     */
    fun matchTemplateAsync(
        sourceBitmap: Bitmap,
        templateBitmap: Bitmap,
        config: MatchConfig = MatchConfig.default()
    ): Deferred<Result<TemplateMatchResult>> {
        return matchingScope.async {
            matchTemplate(sourceBitmap, templateBitmap, config)
        }
    }

    /**
     * 批量匹配多个模板
     */
    suspend fun matchMultipleTemplates(
        sourceBitmap: Bitmap,
        templates: List<Bitmap>,
        config: MatchConfig = MatchConfig.default()
    ): Result<List<TemplateMatchResult>> = withContext(Dispatchers.Default) {
        try {
            val results = mutableListOf<TemplateMatchResult>()

            for (template in templates) {
                val result = matchTemplate(sourceBitmap, template, config)
                result.getOrElse {
                    return@withContext Result.failure(it)
                }.let { results.add(it) }
            }

            Result.success(results)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * 流式匹配，支持实时处理
     */
    fun matchTemplateFlow(
        sourceFlow: Flow<Bitmap>,
        templateBitmap: Bitmap,
        config: MatchConfig = MatchConfig.default()
    ): Flow<Result<TemplateMatchResult>> = flow {
        sourceFlow.collect { sourceBitmap ->
            val result = matchTemplate(sourceBitmap, templateBitmap, config)
            emit(result)
        }
    }.flowOn(Dispatchers.Default)

    /**
     * 性能基准测试
     */
    suspend fun benchmark(
        sourceBitmap: Bitmap,
        templateBitmap: Bitmap,
        iterations: Int = 10,
        config: MatchConfig = MatchConfig.default()
    ): Result<BenchmarkResult> = withContext(Dispatchers.Default) {
        try {
            val times = mutableListOf<Double>()
            val gpuTimes = mutableListOf<Double>()
            val cpuTimes = mutableListOf<Double>()

            // GPU基准测试
            if (isGpuAvailable()) {
                val gpuConfig = config.copy(enableGpu = true)
                repeat(iterations) {
                    val result = matchTemplate(sourceBitmap, templateBitmap, gpuConfig)
                    result.getOrNull()?.performanceStats?.totalTime?.let {
                        gpuTimes.add(it)
                        times.add(it)
                    }
                }
            }

            // CPU基准测试
            val cpuConfig = config.copy(enableGpu = false)
            repeat(iterations) {
                val result = matchTemplate(sourceBitmap, templateBitmap, cpuConfig)
                result.getOrNull()?.performanceStats?.totalTime?.let {
                    cpuTimes.add(it)
                    if (gpuTimes.isEmpty()) times.add(it)
                }
            }

            Result.success(BenchmarkResult(
                iterations = iterations,
                averageTime = times.average(),
                minTime = times.minOrNull() ?: 0.0,
                maxTime = times.maxOrNull() ?: 0.0,
                gpuAverageTime = if (gpuTimes.isNotEmpty()) gpuTimes.average() else null,
                cpuAverageTime = if (cpuTimes.isNotEmpty()) cpuTimes.average() else null,
                gpuSpeedup = if (gpuTimes.isNotEmpty() && cpuTimes.isNotEmpty()) {
                    cpuTimes.average() / gpuTimes.average()
                } else null
            ))

        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * 获取推荐配置
     */
    fun getRecommendedConfig(
        sourceWidth: Int,
        sourceHeight: Int,
        templateWidth: Int,
        templateHeight: Int,
        targetFps: Double = 30.0
    ): MatchConfig {
        val pixels = sourceWidth * sourceHeight
        val templateRatio = (templateWidth * templateHeight).toFloat() / pixels

        return when {
            pixels > 1920 * 1080 -> { // 高分辨率
                if (targetFps >= 60) PresetMode.FAST else PresetMode.BALANCED
            }
            pixels > 1280 * 720 -> { // 中等分辨率
                if (targetFps >= 60) PresetMode.BALANCED else PresetMode.ACCURATE
            }
            templateRatio < 0.01f -> { // 小模板
                PresetMode.SENSITIVE
            }
            else -> PresetMode.BALANCED
        }.let { MatchConfig.fromPreset(it) }
    }
}

/**
 * 基准测试结果
 */
data class BenchmarkResult(
    val iterations: Int,
    val averageTime: Double,
    val minTime: Double,
    val maxTime: Double,
    val gpuAverageTime: Double?,
    val cpuAverageTime: Double?,
    val gpuSpeedup: Double?
) {
    val averageFps: Double
        get() = 1000.0 / averageTime

    val maxFps: Double
        get() = 1000.0 / minTime

    fun getReport(): String {
        return buildString {
            appendLine("=== 性能基准测试报告 ===")
            appendLine("测试次数: $iterations")
            appendLine("平均时间: %.2f ms (%.1f FPS)".format(averageTime, averageFps))
            appendLine("最快时间: %.2f ms (%.1f FPS)".format(minTime, maxFps))
            appendLine("最慢时间: %.2f ms".format(maxTime))

            gpuAverageTime?.let {
                appendLine("GPU平均: %.2f ms (%.1f FPS)".format(it, 1000.0 / it))
            }

            cpuAverageTime?.let {
                appendLine("CPU平均: %.2f ms (%.1f FPS)".format(it, 1000.0 / it))
            }

            gpuSpeedup?.let {
                appendLine("GPU加速比: %.2fx".format(it))
            }
        }
    }
}
