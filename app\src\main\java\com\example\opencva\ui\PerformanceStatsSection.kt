package com.example.opencva.ui

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Speed
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.opencva.PerformanceStats

@Composable
fun PerformanceStatsSection(
    stats: PerformanceStats
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            Text(
                text = "性能统计",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.primary
            )
            
            // 总体性能指标
            OverallPerformanceCard(stats = stats)
            
            // 时间分解图表
            TimeBreakdownChart(stats = stats)
            
            // 详细统计信息
            DetailedStatsGrid(stats = stats)
        }
    }
}

@Composable
private fun OverallPerformanceCard(
    stats: PerformanceStats
) {
    Card(
        colors = CardDefaults.cardColors(
            containerColor = when (stats.performanceLevel) {
                com.example.opencva.PerformanceLevel.EXCELLENT -> 
                    MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.3f)
                com.example.opencva.PerformanceLevel.GOOD -> 
                    Color(0xFF4CAF50).copy(alpha = 0.1f)
                com.example.opencva.PerformanceLevel.FAIR -> 
                    Color(0xFFFF9800).copy(alpha = 0.1f)
                com.example.opencva.PerformanceLevel.POOR -> 
                    MaterialTheme.colorScheme.errorContainer.copy(alpha = 0.3f)
            }
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            Icon(
                imageVector = Icons.Default.Speed,
                contentDescription = "性能",
                modifier = Modifier.size(32.dp),
                tint = MaterialTheme.colorScheme.primary
            )
            
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = "总时间: ${String.format("%.2f", stats.totalTime)} ms",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onSurface
                )
                
                Text(
                    text = "等效FPS: ${String.format("%.1f", stats.fps)}",
                    fontSize = 14.sp,
                    color = MaterialTheme.colorScheme.onSurface
                )
                
                Text(
                    text = "性能等级: ${stats.performanceLevel.description}",
                    fontSize = 12.sp,
                    color = MaterialTheme.colorScheme.onSurface
                )
                
                Text(
                    text = "加速方式: ${stats.accelerationType}",
                    fontSize = 12.sp,
                    color = MaterialTheme.colorScheme.onSurface
                )
            }
        }
    }
}

@Composable
private fun TimeBreakdownChart(
    stats: PerformanceStats
) {
    Column(
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        Text(
            text = "时间分解",
            fontSize = 14.sp,
            fontWeight = FontWeight.Medium,
            color = MaterialTheme.colorScheme.onSurface
        )
        
        Card {
            Column(
                modifier = Modifier.padding(16.dp),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                // 预处理时间
                TimeBreakdownItem(
                    label = "预处理",
                    time = stats.preprocessTime,
                    percentage = stats.preprocessTimePercent,
                    color = Color(0xFF2196F3)
                )
                
                // 匹配时间
                TimeBreakdownItem(
                    label = "匹配处理",
                    time = stats.matchingTime,
                    percentage = stats.matchingTimePercent,
                    color = Color(0xFF4CAF50)
                )
                
                // 后处理时间
                TimeBreakdownItem(
                    label = "后处理",
                    time = stats.postprocessTime,
                    percentage = stats.postprocessTimePercent,
                    color = Color(0xFFFF9800)
                )
            }
        }
    }
}

@Composable
private fun TimeBreakdownItem(
    label: String,
    time: Double,
    percentage: Double,
    color: Color
) {
    Column(
        verticalArrangement = Arrangement.spacedBy(4.dp)
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = label,
                fontSize = 12.sp,
                color = MaterialTheme.colorScheme.onSurface
            )
            
            Text(
                text = "${String.format("%.2f", time)} ms (${String.format("%.1f", percentage)}%)",
                fontSize = 12.sp,
                fontWeight = FontWeight.Medium,
                color = color
            )
        }
        
        // 进度条
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(4.dp)
        ) {
            Canvas(
                modifier = Modifier.fillMaxSize()
            ) {
                // 背景
                drawRect(
                    color = Color.Gray.copy(alpha = 0.2f),
                    size = size
                )
                
                // 进度
                val progressWidth = size.width * (percentage / 100.0).toFloat()
                drawRect(
                    color = color,
                    size = Size(progressWidth, size.height)
                )
            }
        }
    }
}

@Composable
private fun DetailedStatsGrid(
    stats: PerformanceStats
) {
    Column(
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        Text(
            text = "详细信息",
            fontSize = 14.sp,
            fontWeight = FontWeight.Medium,
            color = MaterialTheme.colorScheme.onSurface
        )
        
        Card {
            Column(
                modifier = Modifier.padding(16.dp),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Text(
                        text = "源图像尺寸:",
                        fontSize = 12.sp,
                        color = MaterialTheme.colorScheme.onSurface
                    )
                    Text(
                        text = "${stats.sourceWidth} × ${stats.sourceHeight}",
                        fontSize = 12.sp,
                        fontWeight = FontWeight.Medium,
                        color = MaterialTheme.colorScheme.onSurface
                    )
                }
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Text(
                        text = "模板尺寸:",
                        fontSize = 12.sp,
                        color = MaterialTheme.colorScheme.onSurface
                    )
                    Text(
                        text = "${stats.templateWidth} × ${stats.templateHeight}",
                        fontSize = 12.sp,
                        fontWeight = FontWeight.Medium,
                        color = MaterialTheme.colorScheme.onSurface
                    )
                }
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Text(
                        text = "处理速度:",
                        fontSize = 12.sp,
                        color = MaterialTheme.colorScheme.onSurface
                    )
                    Text(
                        text = "${String.format("%.0f", stats.processingSpeed)} 像素/ms",
                        fontSize = 12.sp,
                        fontWeight = FontWeight.Medium,
                        color = MaterialTheme.colorScheme.onSurface
                    )
                }
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Text(
                        text = "实时性能:",
                        fontSize = 12.sp,
                        color = MaterialTheme.colorScheme.onSurface
                    )
                    Text(
                        text = if (stats.isRealTime) "是 (>30 FPS)" else "否 (<30 FPS)",
                        fontSize = 12.sp,
                        fontWeight = FontWeight.Medium,
                        color = if (stats.isRealTime) 
                            Color(0xFF4CAF50) 
                        else 
                            MaterialTheme.colorScheme.error
                    )
                }
            }
        }
    }
}
