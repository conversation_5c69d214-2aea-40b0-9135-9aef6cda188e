# OpenCV Android 项目构建说明

## 🚨 构建问题解决方案

### 问题1: Java版本兼容性

**错误信息**: `Dependency requires at least JVM runtime version 11. This build uses a Java 8 JVM.`

**解决方案**:
1. **推荐**: 升级到Java 11或更高版本
2. **或者**: 使用项目中已配置的较低版本AGP (7.0.4)

### 问题2: Kotlin编译错误

**错误信息**: 各种Unresolved reference错误

**解决方案**:
1. 确保所有依赖正确配置
2. 检查Kotlin版本兼容性
3. 清理并重新构建项目

## 🔧 推荐的构建环境

### 系统要求
- **Java**: JDK 11+ (推荐JDK 17)
- **Android Studio**: Arctic Fox 2020.3.1+
- **NDK**: 23.1.7779620+
- **CMake**: 3.22.1+

### 环境配置

#### 1. Java环境
```bash
# 检查Java版本
java -version

# 如果版本低于11，请安装JDK 11+
# Windows: 下载Oracle JDK或OpenJDK
# macOS: brew install openjdk@11
# Linux: sudo apt install openjdk-11-jdk
```

#### 2. Android Studio配置
1. 打开Android Studio
2. File → Settings → Build → Build Tools → Gradle
3. 设置Gradle JVM为JDK 11+

#### 3. 环境变量
```bash
# 设置JAVA_HOME
export JAVA_HOME=/path/to/jdk-11
export PATH=$JAVA_HOME/bin:$PATH

# Windows (在系统环境变量中设置)
JAVA_HOME=C:\Program Files\Java\jdk-11
```

## 🛠️ 构建步骤

### 方法1: 使用Android Studio (推荐)
1. 打开Android Studio
2. File → Open → 选择项目根目录
3. 等待Gradle同步完成
4. Build → Make Project

### 方法2: 命令行构建
```bash
# 清理项目
./gradlew clean

# 编译Debug版本
./gradlew assembleDebug

# 编译Release版本
./gradlew assembleRelease
```

## 🔍 故障排除

### 常见问题及解决方案

#### 1. OpenCV SDK路径问题
**错误**: OpenCV相关的CMake错误

**解决方案**:
```kotlin
// 在app/build.gradle.kts中更新OpenCV路径
arguments += listOf(
    "-DOPENCV_ANDROID_SDK_DIR=你的OpenCV路径"
)
```

#### 2. NDK版本不兼容
**错误**: NDK相关编译错误

**解决方案**:
1. 在Android Studio中安装推荐的NDK版本
2. 在local.properties中指定NDK路径:
```properties
ndk.dir=C\:\\Users\\YourName\\AppData\\Local\\Android\\Sdk\\ndk\\23.1.7779620
```

#### 3. Gradle同步失败
**解决方案**:
```bash
# 删除.gradle文件夹
rm -rf .gradle

# 重新同步
./gradlew --refresh-dependencies
```

#### 4. 内存不足错误
**解决方案**:
在gradle.properties中增加内存:
```properties
org.gradle.jvmargs=-Xmx4096m -Dfile.encoding=UTF-8
```

## 📋 版本兼容性矩阵

| AGP版本 | Gradle版本 | Java版本 | Kotlin版本 |
|---------|------------|----------|------------|
| 8.5.2   | 8.7+       | 11+      | 1.9.0+     |
| 7.4.2   | 7.5+       | 11+      | 1.8.0+     |
| 7.0.4   | 7.0+       | 8+       | 1.6.0+     |

## 🚀 快速开始 (如果遇到构建问题)

### 临时解决方案
如果遇到构建问题，可以先使用简化版本:

1. **注释掉复杂的UI组件**:
   - 暂时移除自定义UI组件的引用
   - 使用基本的MainActivity

2. **简化依赖**:
   - 移除不必要的依赖
   - 先确保基本项目能够编译

3. **分步构建**:
   ```bash
   # 只编译Kotlin代码
   ./gradlew compileDebugKotlin
   
   # 只编译C++代码
   ./gradlew externalNativeBuildDebug
   
   # 完整构建
   ./gradlew assembleDebug
   ```

## 📞 获取帮助

如果仍然遇到构建问题:

1. **检查日志**: 使用`--stacktrace --info`获取详细错误信息
2. **清理项目**: 删除build文件夹和.gradle文件夹
3. **检查环境**: 确认Java、Android SDK、NDK版本
4. **更新依赖**: 确保所有依赖都是最新兼容版本

## 📝 注意事项

- 本项目需要OpenCV 5.0 Android SDK
- 确保OpenCV SDK路径正确配置
- 首次构建可能需要下载大量依赖，请耐心等待
- 建议使用稳定的网络环境进行构建
