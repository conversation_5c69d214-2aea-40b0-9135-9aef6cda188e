#include "template_matcher.h"
#include <android/log.h>
#include <algorithm>
#include <cmath>

#define LOG_TAG "TemplateMatcher"
#define LOGI(...) __android_log_print(ANDROID_LOG_INFO, LOG_TAG, __VA_ARGS__)
#define LOGE(...) __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, __VA_ARGS__)

TemplateMatcher::TemplateMatcher() : m_gpuEnabled(true), m_gpuAvailable(false) {
    // 检查OpenCL支持
    m_gpuAvailable = cv::ocl::haveOpenCL();
    if (m_gpuAvailable) {
        cv::ocl::setUseOpenCL(true);
        LOGI("OpenCL GPU acceleration available and enabled");
    } else {
        LOGI("OpenCL GPU acceleration not available, using CPU");
    }
    
    optimizeForDevice();
}

TemplateMatcher::~TemplateMatcher() {
    // 清理资源
}

void TemplateMatcher::setGpuEnabled(bool enable) {
    m_gpuEnabled = enable && m_gpuAvailable;
    cv::ocl::setUseOpenCL(m_gpuEnabled);
    LOGI("GPU acceleration %s", m_gpuEnabled ? "enabled" : "disabled");
}

bool TemplateMatcher::isGpuAvailable() const {
    return m_gpuAvailable;
}

bool TemplateMatcher::matchTemplate(
    const cv::Mat& source,
    const cv::Mat& templateImg,
    MatchMethod method,
    float weakThreshold,
    float strongThreshold,
    float scaleFactor,
    int maxResults,
    const cv::Rect& searchRegion,
    std::vector<MatchResult>& results,
    PerformanceStats& stats
) {
    auto startTime = std::chrono::high_resolution_clock::now();
    
    // 清空结果
    results.clear();
    stats = PerformanceStats();
    
    // 参数验证
    if (source.empty() || templateImg.empty()) {
        LOGE("Source or template image is empty");
        return false;
    }
    
    if (templateImg.cols > source.cols || templateImg.rows > source.rows) {
        LOGE("Template image is larger than source image");
        return false;
    }
    
    // 记录图像尺寸
    stats.sourceWidth = source.cols;
    stats.sourceHeight = source.rows;
    stats.templateWidth = templateImg.cols;
    stats.templateHeight = templateImg.rows;
    stats.gpuUsed = m_gpuEnabled && m_gpuAvailable;
    
    try {
        // 预处理
        cv::Mat processedSource, processedTemplate;
        if (!preprocessImages(source, templateImg, processedSource, processedTemplate,
                             searchRegion, stats.preprocessTime)) {
            LOGE("Failed to preprocess images");
            return false;
        }
        
        // 执行匹配
        cv::Mat matchResult;
        if (!performMatching(processedSource, processedTemplate, method,
                           matchResult, stats.matchingTime)) {
            LOGE("Failed to perform template matching");
            return false;
        }
        
        // 提取结果
        if (!extractResults(matchResult, processedTemplate, method,
                          weakThreshold, strongThreshold, maxResults,
                          results, stats.postprocessTime)) {
            LOGE("Failed to extract results");
            return false;
        }
        
        // 计算总时间
        auto endTime = std::chrono::high_resolution_clock::now();
        stats.totalTime = std::chrono::duration<double, std::milli>(endTime - startTime).count();
        
        LOGI("Template matching completed: %zu results found in %.2fms", 
             results.size(), stats.totalTime);
        
        return true;
        
    } catch (const cv::Exception& e) {
        LOGE("OpenCV exception: %s", e.what());
        return false;
    } catch (const std::exception& e) {
        LOGE("Standard exception: %s", e.what());
        return false;
    }
}

bool TemplateMatcher::preprocessImages(const cv::Mat& source, const cv::Mat& templateImg,
                                     cv::Mat& processedSource, cv::Mat& processedTemplate,
                                     const cv::Rect& searchRegion, double& preprocessTime) {
    auto startTime = std::chrono::high_resolution_clock::now();
    
    try {
        // 转换为灰度图像（如果需要）
        if (source.channels() == 3) {
            cv::cvtColor(source, processedSource, cv::COLOR_BGR2GRAY);
        } else {
            processedSource = source.clone();
        }
        
        if (templateImg.channels() == 3) {
            cv::cvtColor(templateImg, processedTemplate, cv::COLOR_BGR2GRAY);
        } else {
            processedTemplate = templateImg.clone();
        }
        
        // 应用搜索区域
        if (searchRegion.width > 0 && searchRegion.height > 0) {
            cv::Rect validRegion = searchRegion & cv::Rect(0, 0, source.cols, source.rows);
            if (validRegion.width > templateImg.cols && validRegion.height > templateImg.rows) {
                processedSource = processedSource(validRegion);
            }
        }
        
        // 如果启用GPU，转换为UMat
        if (m_gpuEnabled && m_gpuAvailable) {
            cv::UMat uSource, uTemplate;
            processedSource.copyTo(uSource);
            processedTemplate.copyTo(uTemplate);
            processedSource = uSource.getMat(cv::ACCESS_READ);
            processedTemplate = uTemplate.getMat(cv::ACCESS_READ);
        }
        
        auto endTime = std::chrono::high_resolution_clock::now();
        preprocessTime = std::chrono::duration<double, std::milli>(endTime - startTime).count();
        
        return true;
        
    } catch (const cv::Exception& e) {
        LOGE("Preprocessing failed: %s", e.what());
        return false;
    }
}

bool TemplateMatcher::performMatching(const cv::Mat& source, const cv::Mat& templateImg,
                                    MatchMethod method, cv::Mat& result, double& matchingTime) {
    auto startTime = std::chrono::high_resolution_clock::now();

    try {
        if (m_gpuEnabled && m_gpuAvailable) {
            // GPU加速匹配
            cv::UMat uSource, uTemplate, uResult;
            source.copyTo(uSource);
            templateImg.copyTo(uTemplate);

            cv::matchTemplate(uSource, uTemplate, uResult, static_cast<int>(method));
            uResult.copyTo(result);
        } else {
            // CPU匹配
            cv::matchTemplate(source, templateImg, result, static_cast<int>(method));
        }

        auto endTime = std::chrono::high_resolution_clock::now();
        matchingTime = std::chrono::duration<double, std::milli>(endTime - startTime).count();

        return true;

    } catch (const cv::Exception& e) {
        LOGE("Template matching failed: %s", e.what());
        return false;
    }
}

bool TemplateMatcher::extractResults(const cv::Mat& matchResult, const cv::Mat& templateImg,
                                   MatchMethod method, float weakThreshold, float strongThreshold,
                                   int maxResults, std::vector<MatchResult>& results,
                                   double& postprocessTime) {
    auto startTime = std::chrono::high_resolution_clock::now();

    try {
        // 归一化结果
        cv::Mat normalizedResult;
        cv::normalize(matchResult, normalizedResult, 0, 1, cv::NORM_MINMAX);

        // 根据匹配方法确定是否需要反转阈值
        bool invertThreshold = (method == MatchMethod::SQDIFF || method == MatchMethod::SQDIFF_NORMED);
        float effectiveWeakThreshold = invertThreshold ? (1.0f - weakThreshold) : weakThreshold;
        float effectiveStrongThreshold = invertThreshold ? (1.0f - strongThreshold) : strongThreshold;

        // 创建候选点列表
        std::vector<std::pair<cv::Point, float>> candidates;

        for (int y = 0; y < normalizedResult.rows; y++) {
            for (int x = 0; x < normalizedResult.cols; x++) {
                float value = normalizedResult.at<float>(y, x);

                if (invertThreshold) {
                    value = 1.0f - value;
                }

                if (value >= effectiveWeakThreshold) {
                    candidates.emplace_back(cv::Point(x, y), value);
                }
            }
        }

        // 按置信度排序
        std::sort(candidates.begin(), candidates.end(),
                 [](const auto& a, const auto& b) { return a.second > b.second; });

        // 非最大值抑制
        std::vector<bool> suppressed(candidates.size(), false);
        int templateWidth = templateImg.cols;
        int templateHeight = templateImg.rows;

        for (size_t i = 0; i < candidates.size() && results.size() < maxResults; i++) {
            if (suppressed[i]) continue;

            const auto& candidate = candidates[i];

            // 检查是否满足强阈值
            if (candidate.second >= effectiveStrongThreshold) {
                // 获取高精度位置
                cv::Point2f preciseLocation = getHighPrecisionLocation(normalizedResult, candidate.first);

                // 创建边界框
                cv::Rect2f boundingBox(preciseLocation.x, preciseLocation.y,
                                     templateWidth, templateHeight);

                results.emplace_back(preciseLocation, candidate.second, boundingBox);

                // 抑制附近的候选点
                for (size_t j = i + 1; j < candidates.size(); j++) {
                    if (suppressed[j]) continue;

                    float dx = candidates[j].first.x - candidate.first.x;
                    float dy = candidates[j].first.y - candidate.first.y;
                    float distance = std::sqrt(dx * dx + dy * dy);

                    if (distance < std::min(templateWidth, templateHeight) * 0.5f) {
                        suppressed[j] = true;
                    }
                }
            }
        }

        auto endTime = std::chrono::high_resolution_clock::now();
        postprocessTime = std::chrono::duration<double, std::milli>(endTime - startTime).count();

        return true;

    } catch (const cv::Exception& e) {
        LOGE("Result extraction failed: %s", e.what());
        return false;
    }
}

void TemplateMatcher::optimizeForDevice() {
    // 设置OpenCV线程数
    cv::setNumThreads(0); // 使用所有可用核心

    // 设置OpenCL优化
    if (m_gpuAvailable) {
        cv::ocl::Device device = cv::ocl::Device::getDefault();
        LOGI("OpenCL Device: %s", device.name().c_str());
        LOGI("OpenCL Version: %s", device.version().c_str());

        // 设置OpenCL内存池大小
        cv::ocl::setUseOpenCL(true);
    }

    // 启用优化指令集
    cv::setUseOptimized(true);

    LOGI("Device optimization completed");
}

cv::Point2f TemplateMatcher::getHighPrecisionLocation(const cv::Mat& matchResult, cv::Point maxLoc) {
    // 亚像素精度定位
    if (maxLoc.x > 0 && maxLoc.x < matchResult.cols - 1 &&
        maxLoc.y > 0 && maxLoc.y < matchResult.rows - 1) {

        // 使用二次插值获得亚像素精度
        float x0 = matchResult.at<float>(maxLoc.y, maxLoc.x - 1);
        float x1 = matchResult.at<float>(maxLoc.y, maxLoc.x);
        float x2 = matchResult.at<float>(maxLoc.y, maxLoc.x + 1);

        float y0 = matchResult.at<float>(maxLoc.y - 1, maxLoc.x);
        float y1 = matchResult.at<float>(maxLoc.y, maxLoc.x);
        float y2 = matchResult.at<float>(maxLoc.y + 1, maxLoc.x);

        // 计算亚像素偏移
        float dx = 0.5f * (x2 - x0) / (2 * x1 - x0 - x2);
        float dy = 0.5f * (y2 - y0) / (2 * y1 - y0 - y2);

        // 限制偏移范围
        dx = std::max(-0.5f, std::min(0.5f, dx));
        dy = std::max(-0.5f, std::min(0.5f, dy));

        return cv::Point2f(maxLoc.x + dx, maxLoc.y + dy);
    }

    return cv::Point2f(maxLoc.x, maxLoc.y);
}
