# OpenCV 集成指南

由于OpenCV SDK的BuildConfig问题，这里提供几种集成OpenCV的方法：

## 方法1: 使用预编译AAR文件 (推荐)

1. 检查OpenCV SDK目录中是否存在AAR文件：
   ```
   Z:\cheese\OpenCV\OpenCV-5.0-android-sdk\sdk\java\opencv.aar
   ```

2. 如果存在，项目会自动使用AAR文件

3. 如果不存在，需要手动创建AAR文件：
   - 在Android Studio中打开OpenCV SDK项目
   - 构建生成AAR文件
   - 将AAR文件复制到指定位置

## 方法2: 使用Maven仓库 (备用)

如果本地AAR不可用，项目会自动使用Maven仓库中的OpenCV 4.8.0版本。

## 方法3: 手动集成 (如果以上方法都失败)

1. 恢复opencv模块：
   ```kotlin
   // 在settings.gradle.kts中取消注释
   include(":opencv")
   
   // 在app/build.gradle.kts中使用
   implementation(project(":opencv"))
   ```

2. 修复BuildConfig问题：
   - 在OpenCV源码中手动添加BuildConfig导入
   - 或者删除所有BuildConfig相关代码

## 方法4: 使用OpenCV Manager (传统方法)

1. 修改app/build.gradle.kts：
   ```kotlin
   // 移除OpenCV依赖
   // implementation(files("..."))
   ```

2. 在运行时动态加载OpenCV：
   ```kotlin
   private val mLoaderCallback = object : BaseLoaderCallback(this) {
       override fun onManagerConnected(status: Int) {
           when (status) {
               LoaderCallbackInterface.SUCCESS -> {
                   // OpenCV加载成功
               }
               else -> {
                   super.onManagerConnected(status)
               }
           }
       }
   }
   
   override fun onResume() {
       super.onResume()
       if (!OpenCVLoader.initDebug()) {
           OpenCVLoader.initAsync(OpenCVLoader.OPENCV_VERSION, this, mLoaderCallback)
       } else {
           mLoaderCallback.onManagerConnected(LoaderCallbackInterface.SUCCESS)
       }
   }
   ```

## 当前配置状态

项目当前配置为：
1. 优先使用本地AAR文件
2. 如果AAR不存在，使用Maven仓库版本
3. opencv模块已暂时禁用

## 构建建议

1. 首先尝试当前配置构建
2. 如果失败，检查OpenCV SDK路径是否正确
3. 考虑使用OpenCV 4.8.0而不是5.0（更稳定）
4. 如果需要OpenCV 5.0的特定功能，建议手动构建AAR文件

## 故障排除

如果遇到构建问题：

1. 清理项目：`./gradlew clean`
2. 检查OpenCV SDK路径
3. 确认Android SDK和NDK版本兼容性
4. 查看详细错误日志确定具体问题

## 注意事项

- OpenCV 5.0可能存在兼容性问题
- 建议使用OpenCV 4.8.0进行生产环境开发
- 确保NDK版本与OpenCV版本兼容
