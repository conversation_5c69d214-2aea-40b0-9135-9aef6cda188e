# CMake最低版本要求
cmake_minimum_required(VERSION 3.22.1)

# 项目名称
project("opencva")

# C++标准设置
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 编译优化设置
set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} -O3 -DNDEBUG -ffast-math -funroll-loops -fomit-frame-pointer")
set(CMAKE_C_FLAGS_RELEASE "${CMAKE_C_FLAGS_RELEASE} -O3 -DNDEBUG -ffast-math -funroll-loops -fomit-frame-pointer")

# ARM NEON优化
if(ANDROID_ABI STREQUAL "armeabi-v7a")
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -mfpu=neon -mfloat-abi=softfp")
    set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -mfpu=neon -mfloat-abi=softfp")
elseif(ANDROID_ABI STREQUAL "arm64-v8a")
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -march=armv8-a")
    set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -march=armv8-a")
endif()

# OpenCV路径设置
set(OPENCV_ANDROID_SDK_DIR "Z:/cheese/OpenCV/opencv-4.12.0-android-sdk")
set(OpenCV_DIR "${OPENCV_ANDROID_SDK_DIR}/sdk/native/jni")

# 查找OpenCV
find_package(OpenCV REQUIRED)

# 包含目录
include_directories(${OpenCV_INCLUDE_DIRS})
include_directories(${CMAKE_CURRENT_SOURCE_DIR})

# 源文件
set(SOURCES
    template_matcher.cpp
    jni_interface.cpp
)

# 创建共享库
add_library(${CMAKE_PROJECT_NAME} SHARED ${SOURCES})

# 链接库
target_link_libraries(${CMAKE_PROJECT_NAME}
    ${OpenCV_LIBS}
    android
    log
    jnigraphics
)

# 编译器特定优化
if(CMAKE_CXX_COMPILER_ID STREQUAL "Clang")
    target_compile_options(${CMAKE_PROJECT_NAME} PRIVATE
        -Wno-unused-parameter
        -Wno-unused-variable
        -fvisibility=hidden
        -fvisibility-inlines-hidden
    )
endif()

# 链接器优化
set_target_properties(${CMAKE_PROJECT_NAME} PROPERTIES
    LINK_FLAGS "-Wl,--gc-sections -Wl,--strip-all"
)

# 调试信息控制
if(CMAKE_BUILD_TYPE STREQUAL "Release")
    set_target_properties(${CMAKE_PROJECT_NAME} PROPERTIES
        LINK_FLAGS "${CMAKE_SHARED_LINKER_FLAGS} -s"
    )
endif()
