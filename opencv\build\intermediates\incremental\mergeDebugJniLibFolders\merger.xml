<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="Z:\cheese\OPENCVA\opencv\src\main\jniLibs"/><source path="Z:\cheese\OpenCV\OpenCV-5.0-android-sdk\sdk\native\libs"><file name="arm64-v8a/libopencv_java5.so" path="Z:\cheese\OpenCV\OpenCV-5.0-android-sdk\sdk\native\libs\arm64-v8a\libopencv_java5.so"/><file name="armeabi-v7a/libopencv_java5.so" path="Z:\cheese\OpenCV\OpenCV-5.0-android-sdk\sdk\native\libs\armeabi-v7a\libopencv_java5.so"/><file name="x86/libopencv_java5.so" path="Z:\cheese\OpenCV\OpenCV-5.0-android-sdk\sdk\native\libs\x86\libopencv_java5.so"/><file name="x86_64/libopencv_java5.so" path="Z:\cheese\OpenCV\OpenCV-5.0-android-sdk\sdk\native\libs\x86_64\libopencv_java5.so"/></source></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="Z:\cheese\OPENCVA\opencv\src\debug\jniLibs"/></dataSet></merger>