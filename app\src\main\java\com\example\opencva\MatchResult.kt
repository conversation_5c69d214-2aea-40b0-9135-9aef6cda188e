package com.example.opencva

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * 模板匹配结果数据类
 * 
 * @property locationX 匹配位置X坐标
 * @property locationY 匹配位置Y坐标
 * @property confidence 置信度 (0.0 - 1.0)
 * @property boundingBoxX 边界框X坐标
 * @property boundingBoxY 边界框Y坐标
 * @property boundingBoxWidth 边界框宽度
 * @property boundingBoxHeight 边界框高度
 */
@Parcelize
data class MatchResult(
    val locationX: Float,
    val locationY: Float,
    val confidence: Float,
    val boundingBoxX: Float,
    val boundingBoxY: Float,
    val boundingBoxWidth: Float,
    val boundingBoxHeight: Float
) : Parcelable {
    
    /**
     * 获取匹配位置
     */
    val location: Pair<Float, Float>
        get() = Pair(locationX, locationY)
    
    /**
     * 获取边界框
     */
    val boundingBox: BoundingBox
        get() = BoundingBox(boundingBoxX, boundingBoxY, boundingBoxWidth, boundingBoxHeight)
    
    /**
     * 获取置信度百分比
     */
    val confidencePercent: Float
        get() = confidence * 100f
    
    /**
     * 检查是否为高置信度匹配
     */
    fun isHighConfidence(threshold: Float = 0.8f): Boolean {
        return confidence >= threshold
    }
    
    /**
     * 获取匹配中心点
     */
    val centerPoint: Pair<Float, Float>
        get() = Pair(
            boundingBoxX + boundingBoxWidth / 2f,
            boundingBoxY + boundingBoxHeight / 2f
        )
    
    override fun toString(): String {
        return "MatchResult(location=(%.2f, %.2f), confidence=%.3f, boundingBox=(%.2f, %.2f, %.2f, %.2f))".format(
            locationX, locationY, confidence, boundingBoxX, boundingBoxY, boundingBoxWidth, boundingBoxHeight
        )
    }
}

/**
 * 边界框数据类
 */
@Parcelize
data class BoundingBox(
    val x: Float,
    val y: Float,
    val width: Float,
    val height: Float
) : Parcelable {
    
    /**
     * 获取右下角坐标
     */
    val bottomRight: Pair<Float, Float>
        get() = Pair(x + width, y + height)
    
    /**
     * 获取中心点
     */
    val center: Pair<Float, Float>
        get() = Pair(x + width / 2f, y + height / 2f)
    
    /**
     * 获取面积
     */
    val area: Float
        get() = width * height
    
    /**
     * 检查是否包含指定点
     */
    fun contains(pointX: Float, pointY: Float): Boolean {
        return pointX >= x && pointX <= x + width && pointY >= y && pointY <= y + height
    }
    
    /**
     * 检查是否与另一个边界框相交
     */
    fun intersects(other: BoundingBox): Boolean {
        return !(x + width < other.x || other.x + other.width < x ||
                y + height < other.y || other.y + other.height < y)
    }
    
    /**
     * 计算与另一个边界框的交集
     */
    fun intersection(other: BoundingBox): BoundingBox? {
        val left = maxOf(x, other.x)
        val top = maxOf(y, other.y)
        val right = minOf(x + width, other.x + other.width)
        val bottom = minOf(y + height, other.y + other.height)
        
        return if (left < right && top < bottom) {
            BoundingBox(left, top, right - left, bottom - top)
        } else {
            null
        }
    }
    
    /**
     * 计算IoU (Intersection over Union)
     */
    fun iou(other: BoundingBox): Float {
        val intersection = intersection(other) ?: return 0f
        val union = area + other.area - intersection.area
        return if (union > 0) intersection.area / union else 0f
    }
}
