#ifndef TEMPLATE_MATCHER_H
#define TEMPLATE_MATCHER_H

#include <opencv2/opencv.hpp>
#include <opencv2/imgproc.hpp>
#include <opencv2/core/ocl.hpp>
#include <vector>
#include <memory>
#include <chrono>

/**
 * 匹配结果结构体
 */
struct MatchResult {
    cv::Point2f location;      // 匹配位置
    float confidence;          // 置信度
    cv::Rect2f boundingBox;    // 边界框
    
    MatchResult() : location(0, 0), confidence(0.0f), boundingBox(0, 0, 0, 0) {}
    MatchResult(cv::Point2f loc, float conf, cv::Rect2f box) 
        : location(loc), confidence(conf), boundingBox(box) {}
};

/**
 * 性能统计结构体
 */
struct PerformanceStats {
    double preprocessTime;     // 预处理时间(ms)
    double matchingTime;       // 匹配时间(ms)
    double postprocessTime;    // 后处理时间(ms)
    double totalTime;          // 总时间(ms)
    bool gpuUsed;              // 是否使用GPU
    int templateWidth;         // 模板宽度
    int templateHeight;        // 模板高度
    int sourceWidth;           // 源图像宽度
    int sourceHeight;          // 源图像高度
    
    PerformanceStats() : preprocessTime(0), matchingTime(0), postprocessTime(0), 
                        totalTime(0), gpuUsed(false), templateWidth(0), 
                        templateHeight(0), sourceWidth(0), sourceHeight(0) {}
};

/**
 * 高性能模板匹配器类
 */
class TemplateMatcher {
public:
    /**
     * 匹配方法枚举
     */
    enum class MatchMethod {
        SQDIFF = cv::TM_SQDIFF,
        SQDIFF_NORMED = cv::TM_SQDIFF_NORMED,
        CCORR = cv::TM_CCORR,
        CCORR_NORMED = cv::TM_CCORR_NORMED,
        CCOEFF = cv::TM_CCOEFF,
        CCOEFF_NORMED = cv::TM_CCOEFF_NORMED
    };

    /**
     * 构造函数
     */
    TemplateMatcher();
    
    /**
     * 析构函数
     */
    ~TemplateMatcher();

    /**
     * 设置GPU使用偏好
     * @param enable 是否启用GPU加速
     */
    void setGpuEnabled(bool enable);

    /**
     * 检查GPU是否可用
     * @return GPU是否可用
     */
    bool isGpuAvailable() const;

    /**
     * 执行模板匹配
     * @param source 源图像
     * @param templateImg 模板图像
     * @param method 匹配方法
     * @param weakThreshold 弱阈值
     * @param strongThreshold 强阈值
     * @param scaleFactor 缩放因子
     * @param maxResults 最大结果数
     * @param searchRegion 搜索区域(空表示全图)
     * @param results 输出匹配结果
     * @param stats 输出性能统计
     * @return 是否成功
     */
    bool matchTemplate(
        const cv::Mat& source,
        const cv::Mat& templateImg,
        MatchMethod method,
        float weakThreshold,
        float strongThreshold,
        float scaleFactor,
        int maxResults,
        const cv::Rect& searchRegion,
        std::vector<MatchResult>& results,
        PerformanceStats& stats
    );

private:
    bool m_gpuEnabled;
    bool m_gpuAvailable;
    
    // 内部方法
    bool preprocessImages(const cv::Mat& source, const cv::Mat& templateImg,
                         cv::Mat& processedSource, cv::Mat& processedTemplate,
                         const cv::Rect& searchRegion, double& preprocessTime);
    
    bool performMatching(const cv::Mat& source, const cv::Mat& templateImg,
                        MatchMethod method, cv::Mat& result, double& matchingTime);
    
    bool extractResults(const cv::Mat& matchResult, const cv::Mat& templateImg,
                       MatchMethod method, float weakThreshold, float strongThreshold,
                       int maxResults, std::vector<MatchResult>& results,
                       double& postprocessTime);
    
    void optimizeForDevice();
    cv::Point2f getHighPrecisionLocation(const cv::Mat& matchResult, cv::Point maxLoc);
};

#endif // TEMPLATE_MATCHER_H
