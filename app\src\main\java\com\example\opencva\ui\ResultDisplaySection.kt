package com.example.opencva.ui

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.CheckCircle
import androidx.compose.material.icons.filled.Error
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.opencva.MatchResult
import com.example.opencva.TemplateMatchResult

@Composable
fun ResultDisplaySection(
    result: TemplateMatchResult
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Text(
                text = "匹配结果",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.primary
            )
            
            // 结果摘要
            ResultSummaryCard(result = result)
            
            // 详细结果列表
            if (result.hasResults) {
                Text(
                    text = "详细结果 (${result.resultCount})",
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium,
                    color = MaterialTheme.colorScheme.onSurface
                )
                
                Column(
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    result.sortedByConfidence.take(5).forEachIndexed { index, matchResult ->
                        MatchResultItem(
                            result = matchResult,
                            rank = index + 1
                        )
                    }
                    
                    if (result.resultCount > 5) {
                        Card(
                            colors = CardDefaults.cardColors(
                                containerColor = MaterialTheme.colorScheme.surfaceVariant
                            )
                        ) {
                            Text(
                                text = "还有 ${result.resultCount - 5} 个结果...",
                                modifier = Modifier.padding(12.dp),
                                fontSize = 12.sp,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun ResultSummaryCard(
    result: TemplateMatchResult
) {
    Card(
        colors = CardDefaults.cardColors(
            containerColor = if (result.hasResults) 
                MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.3f)
            else 
                MaterialTheme.colorScheme.errorContainer.copy(alpha = 0.3f)
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Icon(
                imageVector = if (result.hasResults) Icons.Default.CheckCircle else Icons.Default.Error,
                contentDescription = if (result.hasResults) "成功" else "失败",
                tint = if (result.hasResults) 
                    MaterialTheme.colorScheme.primary 
                else 
                    MaterialTheme.colorScheme.error
            )
            
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = if (result.hasResults) "匹配成功" else "未找到匹配",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Bold,
                    color = if (result.hasResults) 
                        MaterialTheme.colorScheme.primary 
                    else 
                        MaterialTheme.colorScheme.error
                )
                
                if (result.hasResults) {
                    Text(
                        text = "找到 ${result.resultCount} 个匹配",
                        fontSize = 12.sp,
                        color = MaterialTheme.colorScheme.onSurface
                    )
                    
                    result.bestMatch?.let { best ->
                        Text(
                            text = "最佳置信度: ${String.format("%.3f", best.confidence)}",
                            fontSize = 12.sp,
                            color = MaterialTheme.colorScheme.onSurface
                        )
                    }
                    
                    Text(
                        text = "平均置信度: ${String.format("%.3f", result.averageConfidence)}",
                        fontSize = 12.sp,
                        color = MaterialTheme.colorScheme.onSurface
                    )
                }
            }
        }
    }
}

@Composable
private fun MatchResultItem(
    result: MatchResult,
    rank: Int
) {
    Card(
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(12.dp),
            verticalArrangement = Arrangement.spacedBy(4.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "匹配 #$rank",
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium,
                    color = MaterialTheme.colorScheme.primary
                )
                
                // 置信度指示器
                ConfidenceBadge(confidence = result.confidence)
            }
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = "位置: (${String.format("%.1f", result.locationX)}, ${String.format("%.1f", result.locationY)})",
                    fontSize = 12.sp,
                    color = MaterialTheme.colorScheme.onSurface
                )
                
                Text(
                    text = "置信度: ${String.format("%.3f", result.confidence)}",
                    fontSize = 12.sp,
                    color = MaterialTheme.colorScheme.onSurface
                )
            }
            
            Text(
                text = "边界框: ${String.format("%.0f×%.0f", result.boundingBoxWidth, result.boundingBoxHeight)}",
                fontSize = 12.sp,
                color = MaterialTheme.colorScheme.onSurface
            )
        }
    }
}

@Composable
private fun ConfidenceBadge(
    confidence: Float
) {
    val (color, text) = when {
        confidence >= 0.9f -> MaterialTheme.colorScheme.primary to "优秀"
        confidence >= 0.8f -> Color(0xFF4CAF50) to "良好"
        confidence >= 0.6f -> Color(0xFFFF9800) to "一般"
        else -> MaterialTheme.colorScheme.error to "较差"
    }
    
    Card(
        colors = CardDefaults.cardColors(
            containerColor = color.copy(alpha = 0.1f)
        )
    ) {
        Text(
            text = text,
            modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp),
            fontSize = 10.sp,
            fontWeight = FontWeight.Medium,
            color = color
        )
    }
}
