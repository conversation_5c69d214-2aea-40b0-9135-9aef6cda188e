package com.example.opencva

import android.graphics.Bitmap

/**
 * 模板匹配原生接口类
 * 提供与C++层的JNI通信接口
 */
class TemplateMatcherNative {
    
    companion object {
        // 加载原生库
        init {
            try {
                System.loadLibrary("opencva")
            } catch (e: UnsatisfiedLinkError) {
                throw RuntimeException("Failed to load native library: ${e.message}", e)
            }
        }
        
        /**
         * 匹配方法常量
         */
        object MatchMethod {
            const val SQDIFF = 0
            const val SQDIFF_NORMED = 1
            const val CCORR = 2
            const val CCORR_NORMED = 3
            const val CCOEFF = 4
            const val CCOEFF_NORMED = 5
            
            /**
             * 获取所有可用的匹配方法
             */
            fun getAllMethods(): List<Pair<Int, String>> = listOf(
                SQDIFF to "SQDIFF",
                SQDIFF_NORMED to "SQDIFF_NORMED",
                CCORR to "CCORR",
                CCORR_NORMED to "CCORR_NORMED",
                CCOEFF to "CCOEFF",
                CCOEFF_NORMED to "CCOEFF_NORMED"
            )
            
            /**
             * 获取推荐的匹配方法
             */
            fun getRecommendedMethods(): List<Pair<Int, String>> = listOf(
                CCOEFF_NORMED to "CCOEFF_NORMED (推荐)",
                CCORR_NORMED to "CCORR_NORMED",
                SQDIFF_NORMED to "SQDIFF_NORMED"
            )
            
            /**
             * 获取方法名称
             */
            fun getMethodName(method: Int): String {
                return when (method) {
                    SQDIFF -> "SQDIFF"
                    SQDIFF_NORMED -> "SQDIFF_NORMED"
                    CCORR -> "CCORR"
                    CCORR_NORMED -> "CCORR_NORMED"
                    CCOEFF -> "CCOEFF"
                    CCOEFF_NORMED -> "CCOEFF_NORMED"
                    else -> "UNKNOWN"
                }
            }
            
            /**
             * 检查方法是否需要反转阈值
             */
            fun isInvertedMethod(method: Int): Boolean {
                return method == SQDIFF || method == SQDIFF_NORMED
            }
        }
    }
    
    // 原生方法句柄
    private var nativeHandle: Long = 0
    
    /**
     * 初始化原生匹配器
     */
    fun initialize(): Boolean {
        if (nativeHandle != 0L) {
            destroy()
        }
        nativeHandle = nativeCreate()
        return nativeHandle != 0L
    }
    
    /**
     * 销毁原生匹配器
     */
    fun destroy() {
        if (nativeHandle != 0L) {
            nativeDestroy(nativeHandle)
            nativeHandle = 0L
        }
    }
    
    /**
     * 设置GPU加速启用状态
     */
    fun setGpuEnabled(enabled: Boolean) {
        if (nativeHandle != 0L) {
            nativeSetGpuEnabled(nativeHandle, enabled)
        }
    }
    
    /**
     * 检查GPU是否可用
     */
    fun isGpuAvailable(): Boolean {
        return if (nativeHandle != 0L) {
            nativeIsGpuAvailable(nativeHandle)
        } else false
    }
    
    /**
     * 执行模板匹配
     * 
     * @param sourceBitmap 源图像
     * @param templateBitmap 模板图像
     * @param method 匹配方法
     * @param weakThreshold 弱阈值
     * @param strongThreshold 强阈值
     * @param scaleFactor 缩放因子
     * @param maxResults 最大结果数
     * @param searchRegion 搜索区域 (null表示全图搜索)
     * @return 匹配结果，失败时返回null
     */
    fun matchTemplate(
        sourceBitmap: Bitmap,
        templateBitmap: Bitmap,
        method: Int = MatchMethod.CCOEFF_NORMED,
        weakThreshold: Float = 0.5f,
        strongThreshold: Float = 0.8f,
        scaleFactor: Float = 1.0f,
        maxResults: Int = 10,
        searchRegion: SearchRegion? = null
    ): TemplateMatchResult? {
        if (nativeHandle == 0L) {
            throw IllegalStateException("Native matcher not initialized")
        }
        
        val region = searchRegion ?: SearchRegion.fullImage()
        
        return nativeMatchTemplate(
            nativeHandle,
            sourceBitmap,
            templateBitmap,
            method,
            weakThreshold,
            strongThreshold,
            scaleFactor,
            maxResults,
            region.x,
            region.y,
            region.width,
            region.height
        )
    }
    
    // 原生方法声明
    private external fun nativeCreate(): Long
    private external fun nativeDestroy(handle: Long)
    private external fun nativeSetGpuEnabled(handle: Long, enabled: Boolean)
    private external fun nativeIsGpuAvailable(handle: Long): Boolean
    private external fun nativeMatchTemplate(
        handle: Long,
        sourceBitmap: Bitmap,
        templateBitmap: Bitmap,
        method: Int,
        weakThreshold: Float,
        strongThreshold: Float,
        scaleFactor: Float,
        maxResults: Int,
        searchX: Int,
        searchY: Int,
        searchWidth: Int,
        searchHeight: Int
    ): TemplateMatchResult?
}

/**
 * 搜索区域数据类
 */
data class SearchRegion(
    val x: Int,
    val y: Int,
    val width: Int,
    val height: Int
) {
    companion object {
        /**
         * 创建全图搜索区域
         */
        fun fullImage(): SearchRegion = SearchRegion(0, 0, 0, 0)
        
        /**
         * 从中心点创建搜索区域
         */
        fun fromCenter(centerX: Int, centerY: Int, width: Int, height: Int): SearchRegion {
            return SearchRegion(
                centerX - width / 2,
                centerY - height / 2,
                width,
                height
            )
        }
    }
    
    /**
     * 检查是否为全图搜索
     */
    val isFullImage: Boolean
        get() = width <= 0 || height <= 0
}
