package com.example.opencva

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * 性能统计数据类
 * 
 * @property preprocessTime 预处理时间(毫秒)
 * @property matchingTime 匹配时间(毫秒)
 * @property postprocessTime 后处理时间(毫秒)
 * @property totalTime 总时间(毫秒)
 * @property gpuUsed 是否使用GPU加速
 * @property templateWidth 模板图像宽度
 * @property templateHeight 模板图像高度
 * @property sourceWidth 源图像宽度
 * @property sourceHeight 源图像高度
 */
@Parcelize
data class PerformanceStats(
    val preprocessTime: Double,
    val matchingTime: Double,
    val postprocessTime: Double,
    val totalTime: Double,
    val gpuUsed: Boolean,
    val templateWidth: Int,
    val templateHeight: Int,
    val sourceWidth: Int,
    val sourceHeight: Int
) : Parcelable {
    
    /**
     * 获取预处理时间占比
     */
    val preprocessTimePercent: Double
        get() = if (totalTime > 0) (preprocessTime / totalTime) * 100.0 else 0.0
    
    /**
     * 获取匹配时间占比
     */
    val matchingTimePercent: Double
        get() = if (totalTime > 0) (matchingTime / totalTime) * 100.0 else 0.0
    
    /**
     * 获取后处理时间占比
     */
    val postprocessTimePercent: Double
        get() = if (totalTime > 0) (postprocessTime / totalTime) * 100.0 else 0.0
    
    /**
     * 获取模板图像尺寸
     */
    val templateSize: Pair<Int, Int>
        get() = Pair(templateWidth, templateHeight)
    
    /**
     * 获取源图像尺寸
     */
    val sourceSize: Pair<Int, Int>
        get() = Pair(sourceWidth, sourceHeight)
    
    /**
     * 获取模板图像像素数
     */
    val templatePixels: Int
        get() = templateWidth * templateHeight
    
    /**
     * 获取源图像像素数
     */
    val sourcePixels: Int
        get() = sourceWidth * sourceHeight
    
    /**
     * 计算处理速度 (像素/毫秒)
     */
    val processingSpeed: Double
        get() = if (totalTime > 0) sourcePixels / totalTime else 0.0
    
    /**
     * 获取FPS (假设连续处理)
     */
    val fps: Double
        get() = if (totalTime > 0) 1000.0 / totalTime else 0.0
    
    /**
     * 检查是否为实时性能 (>30 FPS)
     */
    val isRealTime: Boolean
        get() = fps >= 30.0
    
    /**
     * 获取性能等级
     */
    val performanceLevel: PerformanceLevel
        get() = when {
            fps >= 60.0 -> PerformanceLevel.EXCELLENT
            fps >= 30.0 -> PerformanceLevel.GOOD
            fps >= 15.0 -> PerformanceLevel.FAIR
            else -> PerformanceLevel.POOR
        }
    
    /**
     * 获取加速类型描述
     */
    val accelerationType: String
        get() = if (gpuUsed) "GPU加速" else "CPU处理"
    
    /**
     * 获取详细的性能报告
     */
    fun getDetailedReport(): String {
        return buildString {
            appendLine("=== 性能统计报告 ===")
            appendLine("总时间: %.2f ms".format(totalTime))
            appendLine("预处理: %.2f ms (%.1f%%)".format(preprocessTime, preprocessTimePercent))
            appendLine("匹配处理: %.2f ms (%.1f%%)".format(matchingTime, matchingTimePercent))
            appendLine("后处理: %.2f ms (%.1f%%)".format(postprocessTime, postprocessTimePercent))
            appendLine("加速方式: $accelerationType")
            appendLine("处理速度: %.0f 像素/ms".format(processingSpeed))
            appendLine("等效FPS: %.1f".format(fps))
            appendLine("性能等级: ${performanceLevel.description}")
            appendLine("源图像: ${sourceWidth}x${sourceHeight} (${sourcePixels} 像素)")
            appendLine("模板图像: ${templateWidth}x${templateHeight} (${templatePixels} 像素)")
        }
    }
    
    override fun toString(): String {
        return "PerformanceStats(total=%.2fms, gpu=%s, fps=%.1f, level=%s)".format(
            totalTime, if (gpuUsed) "ON" else "OFF", fps, performanceLevel.name
        )
    }
}

/**
 * 性能等级枚举
 */
enum class PerformanceLevel(val description: String) {
    EXCELLENT("优秀 (≥60 FPS)"),
    GOOD("良好 (30-60 FPS)"),
    FAIR("一般 (15-30 FPS)"),
    POOR("较差 (<15 FPS)")
}
