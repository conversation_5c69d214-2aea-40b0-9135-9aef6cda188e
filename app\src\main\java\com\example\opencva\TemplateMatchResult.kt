package com.example.opencva

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * 模板匹配完整结果类
 * 
 * @property results 匹配结果列表
 * @property performanceStats 性能统计信息
 */
@Parcelize
data class TemplateMatchResult(
    val results: List<MatchResult>,
    val performanceStats: PerformanceStats
) : Parcelable {
    
    /**
     * 获取匹配结果数量
     */
    val resultCount: Int
        get() = results.size
    
    /**
     * 检查是否有匹配结果
     */
    val hasResults: Boolean
        get() = results.isNotEmpty()
    
    /**
     * 获取最佳匹配结果
     */
    val bestMatch: MatchResult?
        get() = results.maxByOrNull { it.confidence }
    
    /**
     * 获取高置信度匹配结果
     */
    fun getHighConfidenceResults(threshold: Float = 0.8f): List<MatchResult> {
        return results.filter { it.confidence >= threshold }
    }
    
    /**
     * 按置信度排序的结果
     */
    val sortedByConfidence: List<MatchResult>
        get() = results.sortedByDescending { it.confidence }
    
    /**
     * 获取平均置信度
     */
    val averageConfidence: Float
        get() = if (results.isNotEmpty()) {
            results.map { it.confidence }.average().toFloat()
        } else 0f
    
    /**
     * 获取置信度范围
     */
    val confidenceRange: Pair<Float, Float>?
        get() = if (results.isNotEmpty()) {
            val confidences = results.map { it.confidence }
            Pair(confidences.minOrNull() ?: 0f, confidences.maxOrNull() ?: 0f)
        } else null
    
    /**
     * 检查是否为成功的匹配
     */
    fun isSuccessful(minConfidence: Float = 0.5f): Boolean {
        return hasResults && (bestMatch?.confidence ?: 0f) >= minConfidence
    }
    
    /**
     * 获取匹配摘要
     */
    fun getSummary(): String {
        return buildString {
            appendLine("=== 匹配结果摘要 ===")
            appendLine("匹配数量: $resultCount")
            if (hasResults) {
                appendLine("最佳置信度: %.3f".format(bestMatch?.confidence))
                appendLine("平均置信度: %.3f".format(averageConfidence))
                confidenceRange?.let { (min, max) ->
                    appendLine("置信度范围: %.3f - %.3f".format(min, max))
                }
                appendLine("高置信度匹配: ${getHighConfidenceResults().size}")
            } else {
                appendLine("未找到匹配结果")
            }
            appendLine()
            append(performanceStats.getDetailedReport())
        }
    }
    
    override fun toString(): String {
        return "TemplateMatchResult(results=$resultCount, best=${bestMatch?.confidence ?: 0f}, ${performanceStats})"
    }
}
