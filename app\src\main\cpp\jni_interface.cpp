#include <jni.h>
#include <android/log.h>
#include <android/bitmap.h>
#include "template_matcher.h"
#include <memory>

#define LOG_TAG "JNI_TemplateMatcher"
#define LOGI(...) __android_log_print(ANDROID_LOG_INFO, LOG_TAG, __VA_ARGS__)
#define LOGE(...) __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, __VA_ARGS__)

// 全局模板匹配器实例
static std::unique_ptr<TemplateMatcher> g_matcher;

// 辅助函数：将Android Bitmap转换为OpenCV Mat
cv::Mat bitmapToMat(JNIEnv* env, jobject bitmap) {
    AndroidBitmapInfo info;
    void* pixels;
    
    if (AndroidBitmap_getInfo(env, bitmap, &info) < 0) {
        LOGE("Failed to get bitmap info");
        return cv::Mat();
    }
    
    if (AndroidBitmap_lockPixels(env, bitmap, &pixels) < 0) {
        LOGE("Failed to lock bitmap pixels");
        return cv::Mat();
    }
    
    cv::Mat mat;
    if (info.format == ANDROID_BITMAP_FORMAT_RGBA_8888) {
        mat = cv::Mat(info.height, info.width, CV_8UC4, pixels);
        cv::cvtColor(mat, mat, cv::COLOR_RGBA2BGR);
    } else if (info.format == ANDROID_BITMAP_FORMAT_RGB_565) {
        mat = cv::Mat(info.height, info.width, CV_8UC2, pixels);
        cv::cvtColor(mat, mat, cv::COLOR_BGR5652BGR);
    } else {
        LOGE("Unsupported bitmap format: %d", info.format);
        AndroidBitmap_unlockPixels(env, bitmap);
        return cv::Mat();
    }
    
    // 创建副本以避免内存问题
    cv::Mat result = mat.clone();
    AndroidBitmap_unlockPixels(env, bitmap);
    
    return result;
}

// 辅助函数：创建Java MatchResult对象
jobject createMatchResult(JNIEnv* env, const MatchResult& result) {
    jclass matchResultClass = env->FindClass("com/example/opencva/MatchResult");
    if (!matchResultClass) {
        LOGE("Failed to find MatchResult class");
        return nullptr;
    }
    
    jmethodID constructor = env->GetMethodID(matchResultClass, "<init>", "(FFFFFF)V");
    if (!constructor) {
        LOGE("Failed to find MatchResult constructor");
        return nullptr;
    }
    
    return env->NewObject(matchResultClass, constructor,
                         result.location.x, result.location.y,
                         result.confidence,
                         result.boundingBox.x, result.boundingBox.y,
                         result.boundingBox.width, result.boundingBox.height);
}

// 辅助函数：创建Java PerformanceStats对象
jobject createPerformanceStats(JNIEnv* env, const PerformanceStats& stats) {
    jclass statsClass = env->FindClass("com/example/opencva/PerformanceStats");
    if (!statsClass) {
        LOGE("Failed to find PerformanceStats class");
        return nullptr;
    }
    
    jmethodID constructor = env->GetMethodID(statsClass, "<init>", "(DDDDZIIII)V");
    if (!constructor) {
        LOGE("Failed to find PerformanceStats constructor");
        return nullptr;
    }
    
    return env->NewObject(statsClass, constructor,
                         stats.preprocessTime, stats.matchingTime,
                         stats.postprocessTime, stats.totalTime,
                         stats.gpuUsed,
                         stats.templateWidth, stats.templateHeight,
                         stats.sourceWidth, stats.sourceHeight);
}

extern "C" {

JNIEXPORT jlong JNICALL
Java_com_example_opencva_TemplateMatcherNative_nativeCreate(JNIEnv* env, jobject thiz) {
    try {
        auto matcher = std::make_unique<TemplateMatcher>();
        return reinterpret_cast<jlong>(matcher.release());
    } catch (const std::exception& e) {
        LOGE("Failed to create TemplateMatcher: %s", e.what());
        return 0;
    }
}

JNIEXPORT void JNICALL
Java_com_example_opencva_TemplateMatcherNative_nativeDestroy(JNIEnv* env, jobject thiz, jlong handle) {
    if (handle != 0) {
        auto* matcher = reinterpret_cast<TemplateMatcher*>(handle);
        delete matcher;
    }
}

JNIEXPORT void JNICALL
Java_com_example_opencva_TemplateMatcherNative_nativeSetGpuEnabled(JNIEnv* env, jobject thiz, 
                                                                   jlong handle, jboolean enabled) {
    if (handle != 0) {
        auto* matcher = reinterpret_cast<TemplateMatcher*>(handle);
        matcher->setGpuEnabled(enabled);
    }
}

JNIEXPORT jboolean JNICALL
Java_com_example_opencva_TemplateMatcherNative_nativeIsGpuAvailable(JNIEnv* env, jobject thiz, jlong handle) {
    if (handle != 0) {
        auto* matcher = reinterpret_cast<TemplateMatcher*>(handle);
        return matcher->isGpuAvailable();
    }
    return false;
}

JNIEXPORT jobject JNICALL
Java_com_example_opencva_TemplateMatcherNative_nativeMatchTemplate(
    JNIEnv* env, jobject thiz, jlong handle,
    jobject sourceBitmap, jobject templateBitmap,
    jint method, jfloat weakThreshold, jfloat strongThreshold,
    jfloat scaleFactor, jint maxResults,
    jint searchX, jint searchY, jint searchWidth, jint searchHeight) {

    if (handle == 0) {
        LOGE("Invalid matcher handle");
        return nullptr;
    }

    auto* matcher = reinterpret_cast<TemplateMatcher*>(handle);

    try {
        // 转换Bitmap到Mat
        cv::Mat sourceMat = bitmapToMat(env, sourceBitmap);
        cv::Mat templateMat = bitmapToMat(env, templateBitmap);

        if (sourceMat.empty() || templateMat.empty()) {
            LOGE("Failed to convert bitmaps to Mat");
            return nullptr;
        }

        // 设置搜索区域
        cv::Rect searchRegion;
        if (searchWidth > 0 && searchHeight > 0) {
            searchRegion = cv::Rect(searchX, searchY, searchWidth, searchHeight);
        } else {
            searchRegion = cv::Rect(); // 空区域表示全图搜索
        }

        // 执行匹配
        std::vector<MatchResult> results;
        PerformanceStats stats;

        bool success = matcher->matchTemplate(
            sourceMat, templateMat,
            static_cast<TemplateMatcher::MatchMethod>(method),
            weakThreshold, strongThreshold, scaleFactor, maxResults,
            searchRegion, results, stats
        );

        if (!success) {
            LOGE("Template matching failed");
            return nullptr;
        }

        // 创建结果对象
        jclass resultClass = env->FindClass("com/example/opencva/TemplateMatchResult");
        if (!resultClass) {
            LOGE("Failed to find TemplateMatchResult class");
            return nullptr;
        }

        jmethodID constructor = env->GetMethodID(resultClass, "<init>",
            "(Ljava/util/List;Lcom/example/opencva/PerformanceStats;)V");
        if (!constructor) {
            LOGE("Failed to find TemplateMatchResult constructor");
            return nullptr;
        }

        // 创建结果列表
        jclass arrayListClass = env->FindClass("java/util/ArrayList");
        jmethodID arrayListConstructor = env->GetMethodID(arrayListClass, "<init>", "()V");
        jmethodID addMethod = env->GetMethodID(arrayListClass, "add", "(Ljava/lang/Object;)Z");

        jobject resultList = env->NewObject(arrayListClass, arrayListConstructor);

        for (const auto& result : results) {
            jobject matchResult = createMatchResult(env, result);
            if (matchResult) {
                env->CallBooleanMethod(resultList, addMethod, matchResult);
                env->DeleteLocalRef(matchResult);
            }
        }

        // 创建性能统计对象
        jobject performanceStats = createPerformanceStats(env, stats);

        // 创建最终结果对象
        jobject finalResult = env->NewObject(resultClass, constructor, resultList, performanceStats);

        // 清理本地引用
        env->DeleteLocalRef(resultList);
        if (performanceStats) {
            env->DeleteLocalRef(performanceStats);
        }

        return finalResult;

    } catch (const std::exception& e) {
        LOGE("Exception in template matching: %s", e.what());
        return nullptr;
    }
}

} // extern "C"
