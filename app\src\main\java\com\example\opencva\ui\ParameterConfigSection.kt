package com.example.opencva.ui

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.selection.selectable
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.opencva.TemplateMatcher
import com.example.opencva.TemplateMatcherNative

@Composable
fun ParameterConfigSection(
    config: TemplateMatcher.MatchConfig,
    onConfigChange: (TemplateMatcher.MatchConfig) -> Unit,
    presetMode: TemplateMatcher.PresetMode,
    onPresetChange: (TemplateMatcher.PresetMode) -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            Text(
                text = "参数配置",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.primary
            )
            
            // 预设模式选择
            PresetModeSelection(
                selectedMode = presetMode,
                onModeChange = onPresetChange
            )
            
            // 匹配方法选择
            MatchMethodSelection(
                selectedMethod = config.method,
                onMethodChange = { method ->
                    onConfigChange(config.copy(method = method))
                }
            )
            
            // 阈值配置
            ThresholdConfiguration(
                weakThreshold = config.weakThreshold,
                strongThreshold = config.strongThreshold,
                onWeakThresholdChange = { threshold ->
                    onConfigChange(config.copy(weakThreshold = threshold))
                },
                onStrongThresholdChange = { threshold ->
                    onConfigChange(config.copy(strongThreshold = threshold))
                }
            )
            
            // 其他参数
            OtherParametersConfiguration(
                config = config,
                onConfigChange = onConfigChange
            )
        }
    }
}

@Composable
private fun PresetModeSelection(
    selectedMode: TemplateMatcher.PresetMode,
    onModeChange: (TemplateMatcher.PresetMode) -> Unit
) {
    Column(
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        Text(
            text = "预设模式",
            fontSize = 14.sp,
            fontWeight = FontWeight.Medium,
            color = MaterialTheme.colorScheme.onSurface
        )
        
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            TemplateMatcher.PresetMode.values().forEach { mode ->
                FilterChip(
                    selected = selectedMode == mode,
                    onClick = { onModeChange(mode) },
                    label = { 
                        Text(
                            text = mode.displayName,
                            fontSize = 12.sp
                        ) 
                    },
                    modifier = Modifier.weight(1f)
                )
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun MatchMethodSelection(
    selectedMethod: Int,
    onMethodChange: (Int) -> Unit
) {
    var expanded by remember { mutableStateOf(false) }
    val methods = TemplateMatcherNative.MatchMethod.getRecommendedMethods()

    Column(
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        Text(
            text = "匹配方法",
            fontSize = 14.sp,
            fontWeight = FontWeight.Medium,
            color = MaterialTheme.colorScheme.onSurface
        )

        ExposedDropdownMenuBox(
            expanded = expanded,
            onExpandedChange = { expanded = !expanded }
        ) {
            OutlinedTextField(
                value = TemplateMatcherNative.MatchMethod.getMethodName(selectedMethod),
                onValueChange = { },
                readOnly = true,
                trailingIcon = { ExposedDropdownMenuDefaults.TrailingIcon(expanded = expanded) },
                modifier = Modifier
                    .fillMaxWidth()
                    .menuAnchor()
            )

            ExposedDropdownMenu(
                expanded = expanded,
                onDismissRequest = { expanded = false }
            ) {
                methods.forEach { (method: Int, name: String) ->
                    DropdownMenuItem(
                        text = { Text(name) },
                        onClick = {
                            onMethodChange(method)
                            expanded = false
                        }
                    )
                }
            }
        }
    }
}

@Composable
private fun ThresholdConfiguration(
    weakThreshold: Float,
    strongThreshold: Float,
    onWeakThresholdChange: (Float) -> Unit,
    onStrongThresholdChange: (Float) -> Unit
) {
    Column(
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        Text(
            text = "阈值配置",
            fontSize = 14.sp,
            fontWeight = FontWeight.Medium,
            color = MaterialTheme.colorScheme.onSurface
        )
        
        // 弱阈值
        Column(
            verticalArrangement = Arrangement.spacedBy(4.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "弱阈值",
                    fontSize = 12.sp,
                    color = MaterialTheme.colorScheme.onSurface
                )
                Text(
                    text = String.format("%.2f", weakThreshold),
                    fontSize = 12.sp,
                    fontWeight = FontWeight.Medium,
                    color = MaterialTheme.colorScheme.primary
                )
            }
            
            Slider(
                value = weakThreshold,
                onValueChange = onWeakThresholdChange,
                valueRange = 0f..1f,
                steps = 19
            )
        }
        
        // 强阈值
        Column(
            verticalArrangement = Arrangement.spacedBy(4.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "强阈值",
                    fontSize = 12.sp,
                    color = MaterialTheme.colorScheme.onSurface
                )
                Text(
                    text = String.format("%.2f", strongThreshold),
                    fontSize = 12.sp,
                    fontWeight = FontWeight.Medium,
                    color = MaterialTheme.colorScheme.primary
                )
            }
            
            Slider(
                value = strongThreshold,
                onValueChange = onStrongThresholdChange,
                valueRange = 0f..1f,
                steps = 19
            )
        }
    }
}

@Composable
private fun OtherParametersConfiguration(
    config: TemplateMatcher.MatchConfig,
    onConfigChange: (TemplateMatcher.MatchConfig) -> Unit
) {
    Column(
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        Text(
            text = "其他参数",
            fontSize = 14.sp,
            fontWeight = FontWeight.Medium,
            color = MaterialTheme.colorScheme.onSurface
        )
        
        // 最大结果数
        Column(
            verticalArrangement = Arrangement.spacedBy(4.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "最大结果数",
                    fontSize = 12.sp,
                    color = MaterialTheme.colorScheme.onSurface
                )
                Text(
                    text = config.maxResults.toString(),
                    fontSize = 12.sp,
                    fontWeight = FontWeight.Medium,
                    color = MaterialTheme.colorScheme.primary
                )
            }
            
            Slider(
                value = config.maxResults.toFloat(),
                onValueChange = { value ->
                    onConfigChange(config.copy(maxResults = value.toInt()))
                },
                valueRange = 1f..20f,
                steps = 18
            )
        }
        
        // GPU加速开关
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "GPU加速",
                fontSize = 12.sp,
                color = MaterialTheme.colorScheme.onSurface
            )
            
            Switch(
                checked = config.enableGpu,
                onCheckedChange = { enabled ->
                    onConfigChange(config.copy(enableGpu = enabled))
                }
            )
        }
    }
}
