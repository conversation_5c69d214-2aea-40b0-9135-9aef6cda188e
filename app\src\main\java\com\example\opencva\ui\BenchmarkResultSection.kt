package com.example.opencva.ui

import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Assessment
import androidx.compose.material.icons.filled.Computer
import androidx.compose.material.icons.filled.Memory
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.opencva.BenchmarkResult

@Composable
fun BenchmarkResultSection(
    benchmark: BenchmarkResult
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            Text(
                text = "基准测试结果",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.primary
            )
            
            // 测试概览
            BenchmarkOverviewCard(benchmark = benchmark)
            
            // 性能对比
            if (benchmark.gpuAverageTime != null && benchmark.cpuAverageTime != null) {
                PerformanceComparisonCard(benchmark = benchmark)
            }
            
            // 详细统计
            DetailedBenchmarkStats(benchmark = benchmark)
        }
    }
}

@Composable
private fun BenchmarkOverviewCard(
    benchmark: BenchmarkResult
) {
    Card(
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.3f)
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            Icon(
                imageVector = Icons.Default.Assessment,
                contentDescription = "基准测试",
                modifier = Modifier.size(32.dp),
                tint = MaterialTheme.colorScheme.primary
            )
            
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = "测试次数: ${benchmark.iterations}",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onSurface
                )
                
                Text(
                    text = "平均时间: ${String.format("%.2f", benchmark.averageTime)} ms",
                    fontSize = 14.sp,
                    color = MaterialTheme.colorScheme.onSurface
                )
                
                Text(
                    text = "平均FPS: ${String.format("%.1f", benchmark.averageFps)}",
                    fontSize = 14.sp,
                    color = MaterialTheme.colorScheme.onSurface
                )
                
                Text(
                    text = "最高FPS: ${String.format("%.1f", benchmark.maxFps)}",
                    fontSize = 12.sp,
                    color = MaterialTheme.colorScheme.onSurface
                )
            }
        }
    }
}

@Composable
private fun PerformanceComparisonCard(
    benchmark: BenchmarkResult
) {
    Column(
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        Text(
            text = "GPU vs CPU 性能对比",
            fontSize = 14.sp,
            fontWeight = FontWeight.Medium,
            color = MaterialTheme.colorScheme.onSurface
        )
        
        Card {
            Column(
                modifier = Modifier.padding(16.dp),
                verticalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                // GPU性能
                benchmark.gpuAverageTime?.let { gpuTime ->
                    PerformanceComparisonItem(
                        icon = Icons.Default.Memory,
                        label = "GPU加速",
                        time = gpuTime,
                        fps = 1000.0 / gpuTime,
                        color = Color(0xFF4CAF50),
                        isBetter = true
                    )
                }
                
                // CPU性能
                benchmark.cpuAverageTime?.let { cpuTime ->
                    PerformanceComparisonItem(
                        icon = Icons.Default.Computer,
                        label = "CPU处理",
                        time = cpuTime,
                        fps = 1000.0 / cpuTime,
                        color = Color(0xFF2196F3),
                        isBetter = false
                    )
                }
                
                // 加速比
                benchmark.gpuSpeedup?.let { speedup ->
                    Divider()
                    
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "GPU加速比:",
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Medium,
                            color = MaterialTheme.colorScheme.onSurface
                        )
                        
                        Card(
                            colors = CardDefaults.cardColors(
                                containerColor = when {
                                    speedup >= 2.0 -> Color(0xFF4CAF50).copy(alpha = 0.1f)
                                    speedup >= 1.5 -> Color(0xFFFF9800).copy(alpha = 0.1f)
                                    else -> MaterialTheme.colorScheme.errorContainer.copy(alpha = 0.3f)
                                }
                            )
                        ) {
                            Text(
                                text = "${String.format("%.2f", speedup)}x",
                                modifier = Modifier.padding(horizontal = 12.dp, vertical = 6.dp),
                                fontSize = 14.sp,
                                fontWeight = FontWeight.Bold,
                                color = when {
                                    speedup >= 2.0 -> Color(0xFF4CAF50)
                                    speedup >= 1.5 -> Color(0xFFFF9800)
                                    else -> MaterialTheme.colorScheme.error
                                }
                            )
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun PerformanceComparisonItem(
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    label: String,
    time: Double,
    fps: Double,
    color: Color,
    isBetter: Boolean
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        Icon(
            imageVector = icon,
            contentDescription = label,
            tint = color,
            modifier = Modifier.size(24.dp)
        )
        
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = label,
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium,
                color = MaterialTheme.colorScheme.onSurface
            )
            
            Text(
                text = "${String.format("%.2f", time)} ms • ${String.format("%.1f", fps)} FPS",
                fontSize = 12.sp,
                color = MaterialTheme.colorScheme.onSurface
            )
        }
        
        if (isBetter) {
            Card(
                colors = CardDefaults.cardColors(
                    containerColor = color.copy(alpha = 0.1f)
                )
            ) {
                Text(
                    text = "更快",
                    modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp),
                    fontSize = 10.sp,
                    fontWeight = FontWeight.Medium,
                    color = color
                )
            }
        }
    }
}

@Composable
private fun DetailedBenchmarkStats(
    benchmark: BenchmarkResult
) {
    Column(
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        Text(
            text = "详细统计",
            fontSize = 14.sp,
            fontWeight = FontWeight.Medium,
            color = MaterialTheme.colorScheme.onSurface
        )
        
        Card {
            Column(
                modifier = Modifier.padding(16.dp),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Text(
                        text = "最快时间:",
                        fontSize = 12.sp,
                        color = MaterialTheme.colorScheme.onSurface
                    )
                    Text(
                        text = "${String.format("%.2f", benchmark.minTime)} ms",
                        fontSize = 12.sp,
                        fontWeight = FontWeight.Medium,
                        color = Color(0xFF4CAF50)
                    )
                }
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Text(
                        text = "最慢时间:",
                        fontSize = 12.sp,
                        color = MaterialTheme.colorScheme.onSurface
                    )
                    Text(
                        text = "${String.format("%.2f", benchmark.maxTime)} ms",
                        fontSize = 12.sp,
                        fontWeight = FontWeight.Medium,
                        color = MaterialTheme.colorScheme.error
                    )
                }
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Text(
                        text = "时间变化:",
                        fontSize = 12.sp,
                        color = MaterialTheme.colorScheme.onSurface
                    )
                    Text(
                        text = "${String.format("%.2f", benchmark.maxTime - benchmark.minTime)} ms",
                        fontSize = 12.sp,
                        fontWeight = FontWeight.Medium,
                        color = MaterialTheme.colorScheme.onSurface
                    )
                }
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Text(
                        text = "稳定性:",
                        fontSize = 12.sp,
                        color = MaterialTheme.colorScheme.onSurface
                    )
                    
                    val stability = 1.0 - ((benchmark.maxTime - benchmark.minTime) / benchmark.averageTime)
                    val stabilityPercent = (stability * 100).coerceIn(0.0, 100.0)
                    
                    Text(
                        text = "${String.format("%.1f", stabilityPercent)}%",
                        fontSize = 12.sp,
                        fontWeight = FontWeight.Medium,
                        color = when {
                            stabilityPercent >= 90 -> Color(0xFF4CAF50)
                            stabilityPercent >= 70 -> Color(0xFFFF9800)
                            else -> MaterialTheme.colorScheme.error
                        }
                    )
                }
            }
        }
    }
}
